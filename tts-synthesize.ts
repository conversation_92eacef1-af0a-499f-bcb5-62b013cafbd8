import { GoogleGenAI } from '@google/genai';
import mime from 'mime';
import { writeFile } from 'fs';
import ffmpeg from 'fluent-ffmpeg';
import ffmpegInstaller from '@ffmpeg-installer/ffmpeg';

ffmpeg.setFfmpegPath(ffmpegInstaller.path);

function convertWavToMp3(wavFile: string, mp3File: string) {
  ffmpeg(wavFile)
    .toFormat('mp3')
    .on('end', () => {
      console.log(`Converted ${wavFile} to ${mp3File}`);
    })
    .on('error', (err) => {
      console.error(`Error converting file: ${err.message}`);
    })
    .save(mp3File);
}

const GEMINI_API_KEY = 'AIzaSyA-kXPgR57irUBhsJy6wIkiafWLoTv5qg8';
const MODEL = 'gemini-2.5-flash-preview-tts';

function saveBinaryFile(fileName: string, content: Buffer) {
  writeFile(fileName, content, 'utf8', (err) => {
    if (err) {
      console.error(`Error writing file ${fileName}:`, err);
      return;
    }
    console.log(`File ${fileName} saved to file system.`);
    if (fileName.endsWith('.wav')) {
      const mp3File = fileName.replace(/\.wav$/, '.mp3');
      convertWavToMp3(fileName, mp3File);
    }
  });
}

async function main() {
  const ai = new GoogleGenAI({
    apiKey: GEMINI_API_KEY,
  });
  const config = {
    temperature: 1,
    responseModalities: [
        'audio',
    ],
    speechConfig: {
      voiceConfig: {
        prebuiltVoiceConfig: {
          voiceName: 'Zephyr',
        }
      }
    },
  };
  const contents = [
    {
      role: 'user',
      parts: [
        {
          text: `của ba ngày qua. “Cảm ơn,” tôi nói với Ito. “Ông sẽ không bao giờ hối hận vì đã bảo vệ chúng tôi như vậy.”

Ông ta chỉnh lại cà vạt. “Thật là ngu ngốc,” ông ta nói.

Lúc đầu tôi nghĩ ông ta đang nói về tôi. Rồi tôi nhận ra ý ông ta là ngân hàng. “Tôi không thích sự ngu ngốc,” ông ta nói. “Mọi người quá chú ý đến những con số.”`,
        },
      ],
    },
  ];

  console.log('<><><> config:', config);
  console.log('<><><> contents:', contents);

  const response = await ai.models.generateContentStream({
    model: MODEL,
    config,
    contents,
  });

  console.log('<><><> response:', response);

  let fileIndex = 0;
  for await (const chunk of response) {
    if (!chunk.candidates || !chunk.candidates[0].content || !chunk.candidates[0].content.parts) {
      continue;
    }
    if (chunk.candidates?.[0]?.content?.parts?.[0]?.inlineData) {
      const fileName = `ENTER_FILE_NAME_${fileIndex++}`;
      const inlineData = chunk.candidates[0].content.parts[0].inlineData;
      let fileExtension = mime.getExtension(inlineData.mimeType || '');
      let buffer = Buffer.from(inlineData.data || '', 'base64');
      if (!fileExtension) {
        fileExtension = 'wav';
        // buffer = convertToWav(inlineData.data || '', inlineData.mimeType || '');
      }
      saveBinaryFile(`${fileName}.${fileExtension}`, buffer);
    }
    else {
      console.log(chunk.text);
    }
  }
}
main();
