import { GoogleGenAI } from '@google/genai';
import mime from 'mime';
import { writeFile } from 'fs';
import ffmpeg from 'fluent-ffmpeg';
import ffmpegInstaller from '@ffmpeg-installer/ffmpeg';

ffmpeg.setFfmpegPath(ffmpegInstaller.path);

function convertWavToMp3(wavFile: string, mp3File: string) {
  console.log(`Starting conversion: ${wavFile} -> ${mp3File}`);

  ffmpeg(wavFile)
    .toFormat('mp3')
    .audioCodec('libmp3lame')
    .audioBitrate(128)
    .on('start', (commandLine: string) => {
      console.log('FFmpeg command:', commandLine);
    })
    .on('end', () => {
      console.log(`Successfully converted ${wavFile} to ${mp3File}`);
    })
    .on('error', (err: Error) => {
      console.error(`Error converting file: ${err.message}`);
      console.error('Full error:', err);
    })
    .save(mp3File);
}

const GEMINI_API_KEY = 'AIzaSyA-kXPgR57irUBhsJy6wIkiafWLoTv5qg8';
const MODEL = 'gemini-2.5-flash-preview-tts';

function saveBinaryFile(fileName: string, content: Buffer, startTime: number) {
  writeFile(fileName, content, (err) => {
    if (err) {
      console.error(`Error writing file ${fileName}:`, err);
      return;
    }
    const elapsedTime = Date.now() - startTime;
    console.log(`File ${fileName} saved to file system. Elapsed time: ${elapsedTime}ms (${(elapsedTime / 1000).toFixed(2)}s)`);
    if (fileName.endsWith('.wav')) {
      const mp3File = fileName.replace(/\.wav$/, '.mp3');
      convertWavToMp3(fileName, mp3File);
    }
  });
}

async function main() {
  const ai = new GoogleGenAI({
    apiKey: GEMINI_API_KEY,
  });
  const config = {
    temperature: 1,
    responseModalities: [
        'audio',
    ],
    speechConfig: {
      voiceConfig: {
        prebuiltVoiceConfig: {
          voiceName: 'Zephyr',
        }
      }
    },
  };
  const contents = [
    {
      role: 'user',
      parts: [
        {
          text: `Hoàng tử bé đang đi dạo thì gặp một con cáo nhỏ.

- Xin chào! - Con cáo lên tiếng trước.
- Xin chào! - Hoàng tử bé đáp lại, hơi ngạc nhiên, cậu quay đầu nhìn quanh.

Cậu bé không thấy ai cả, chỉ có cánh đồng lúa vàng và những hàng cây lặng gió.
- Tớ ở đây, dưới gốc cây táo này - Con cáo nói.

Hoàng tử bé bước lại gần, nhìn thấy một con vật nhỏ bé với bộ lông óng ánh, đôi mắt sáng lạ thường.
- Cậu là ai thế? - Cậu hỏi. - Trông cậu thật dễ thương.
- Tớ là cáo - Con vật trả lời đơn giản.
- Lại chơi với tớ đi! - Hoàng tử bé đề nghị. - Tớ buồn lắm, đi lang thang một mình hoài.

Con cáo lắc đầu:
- Tớ không thể chơi với cậu được. Tớ chưa được cậu thuần hóa.

Hoàng tử bé nghiêng đầu:
- Thuần hóa nghĩa là gì?

Con cáo mỉm cười, ánh mắt hiền hậu mà có chút buồn:
- Thuần hóa là một điều đã bị loài người lãng quên từ lâu rồi. Nó có nghĩa là tạo ra những mối liên hệ.

- Tạo ra liên hệ ư? - Hoàng tử bé lặp lại.
- Đúng vậy - Con cáo giải thích. - Bây giờ, đối với tớ, cậu chỉ là một cậu bé giống như hàng vạn cậu bé khác trên thế giới. Tớ không cần đến cậu, và cậu cũng chẳng cần đến tớ. Nhưng nếu cậu thuần hóa tớ, chúng mình sẽ cần đến nhau. Cậu sẽ trở thành duy nhất trên đời đối với tớ. Và tớ cũng sẽ là duy nhất trên đời đối với cậu.

Hoàng tử bé im lặng suy nghĩ. Sau một lúc, cậu khẽ nói:
- Tớ có một bông hoa. Hình như nó đã thuần hóa tớ rồi...

- Có thể lắm - Con cáo khẽ gật đầu. - Ở Trái đất này người ta thấy đủ các thứ kỳ lạ lắm.

Rồi nó nói thêm, giọng bỗng trở nên tha thiết:
- Cuộc đời tớ vốn rất đơn điệu. Tớ săn gà, người thì săn tớ. Tất cả gà đều giống nhau, tất cả người đều giống nhau. Nên cuộc đời thật tẻ nhạt. Nhưng nếu cậu thuần hóa tớ, cuộc sống của tớ sẽ tràn đầy ánh sáng. Tớ sẽ nhận ra tiếng bước chân của cậu, phân biệt nó với mọi tiếng bước chân khác. Tiếng bước chân của những người xa lạ làm tớ hoảng sợ, khiến tớ trốn xuống hang. Nhưng tiếng bước chân của cậu sẽ giống như khúc nhạc, gọi tớ ra khỏi chỗ nấp.

Đôi mắt con cáo long lanh như mơ tưởng đến viễn cảnh ấy. Nó lại nói tiếp:
- Và cậu thấy kia không, cánh đồng lúa vàng trải dài bất tận? Tớ chẳng hề quan tâm đến lúa mì, bởi tớ không ăn bánh mì. Nhưng cậu có mái tóc vàng óng. Nếu cậu thuần hóa tớ, lúa mì kia sẽ làm tớ nhớ đến cậu. Tớ sẽ yêu tiếng gió thổi qua những cánh đồng lúa, vì nó gợi nhắc hình ảnh về mái tóc vàng của cậu.

Hoàng tử bé bỗng thấy lòng mình rung động. Cậu nói:
- Tớ rất muốn thuần hóa cậu. Nhưng tớ không có nhiều thời gian. Tớ còn phải tìm bạn bè, và phải học biết nhiều điều nữa.

Con cáo chậm rãi lắc đầu:
- Người ta chỉ thật sự hiểu điều gì khi người ta đã thuần hóa nó. Con người bây giờ chẳng còn đủ kiên nhẫn để hiểu. Họ mua mọi thứ đã được làm sẵn trong các cửa hàng. Nhưng không có cửa hàng nào bán tình bạn, vì thế họ chẳng có bạn bè. Nếu cậu muốn có bạn, hãy thuần hóa tớ đi!

Hoàng tử bé băn khoăn:
- Thuần hóa cậu thì phải làm thế nào?

- Cần phải kiên nhẫn - Con cáo giải thích. - Trước hết, cậu hãy ngồi xa xa tớ một chút, như thế này, trên bãi cỏ. Đừng nói gì cả, bởi ngôn ngữ có thể gây hiểu lầm. Thay vào đó, hãy nhìn tớ bằng ánh mắt hiền hòa. Mỗi ngày, cậu hãy đến gần hơn một chút. Cứ như thế, từng chút một, chúng ta sẽ xích lại gần nhau.

Ngày hôm sau, hoàng tử bé trở lại. Con cáo nhìn cậu từ xa, đôi mắt ánh lên niềm vui thầm kín.
- Cậu nên đến vào cùng một giờ mỗi ngày - Nó nói. - Như thế, tớ sẽ biết trước và lòng tớ sẽ rộn ràng. Ví dụ, nếu cậu đến vào lúc bốn giờ chiều, từ ba giờ tớ đã bắt đầu thấy hạnh phúc. Càng gần giờ gặp, tớ càng háo hức. Nhưng nếu cậu đến bất chợt, tớ sẽ không biết lúc nào để chuẩn bị cho niềm vui ấy.

Hoàng tử bé gật gù, như hiểu ra một quy luật bí mật của trái tim.

Nhiều ngày trôi qua, cậu bé nhỏ dần thuần hóa con cáo bằng sự kiên nhẫn, bằng những buổi ngồi bên nhau trong im lặng, bằng những ánh nhìn hiền hòa. Và rồi đến lúc chia tay, con cáo khóc.

- À, thì ra cậu sẽ khóc ư? - Hoàng tử bé ngạc nhiên.
- Đúng thế - Con cáo trả lời. - Nhưng đó là lỗi của cậu. Chính cậu đã khiến tớ gắn bó, đã để tớ cảm nhận được sự thân thiết này.

- Nhưng tớ chẳng muốn làm cậu buồn... - Hoàng tử bé nói nhỏ.
- Khi đã để ai đó thuần hóa mình, thì chắc chắn sẽ có những giọt nước mắt. Nhưng đồng thời, cũng có niềm hạnh phúc. Nhờ cậu, tớ sẽ luôn nhớ đến cánh đồng lúa vàng, đến mái tóc vàng óng.

Rồi con cáo thì thầm:
- Cậu hãy ghi nhớ một bí mật này. Nó rất đơn giản: chỉ có trái tim mới nhìn thấy rõ. Điều cốt yếu thì mắt thường không thể nhìn thấy.

Hoàng tử bé lặp lại để ghi nhớ:
- Điều cốt yếu thì mắt thường không thể nhìn thấy.

- Và một điều nữa - Con cáo nói - thời gian mà cậu đã dành cho bông hoa của cậu làm cho nó trở nên quan trọng đối với cậu.

Hoàng tử bé hiểu ra, lòng bỗng dâng lên một nỗi thương nhớ khôn tả về bông hoa duy nhất trên hành tinh nhỏ bé của mình.`
        }
      ],
    },
  ];

  console.log('<><><> config:', config);
  // console.log('<><><> contents:', contents);

  const startTime = Date.now();
  console.log(`Starting TTS generation at ${new Date(startTime).toISOString()}`);

  const response = await ai.models.generateContentStream({
    model: MODEL,
    config,
    contents,
  });

  console.log('<><><> response:', response);

  let fileIndex = 0;
  for await (const chunk of response) {
    if (!chunk.candidates || !chunk.candidates[0].content || !chunk.candidates[0].content.parts) {
      continue;
    }
    if (chunk.candidates?.[0]?.content?.parts?.[0]?.inlineData) {
      const fileName = `tts_output_${fileIndex++}`;
      const inlineData = chunk.candidates[0].content.parts[0].inlineData;
      let fileExtension = mime.getExtension(inlineData.mimeType || '');
      let buffer = Buffer.from(inlineData.data || '', 'base64');
      if (!fileExtension) {
        fileExtension = 'wav';
        // buffer = convertToWav(inlineData.data || '', inlineData.mimeType || '');
      }
      saveBinaryFile(`${fileName}.${fileExtension}`, buffer, startTime);
    }
    else {
      console.log(chunk.text);
    }
  }

  const totalElapsedTime = Date.now() - startTime;
  console.log(`\nTTS generation completed. Total elapsed time: ${totalElapsedTime}ms (${(totalElapsedTime / 1000).toFixed(2)}s)`);
  console.log(`Generated ${fileIndex} audio file(s).`);
}
main();
